package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.model.wrappers.dilazione.DilazioneWrapper;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazione;
import com.optimaitalia.model.wrappers.dilazione.RichiediDilazioneRequest;
import com.optimaitalia.service.DilazioneClienteService;
import com.optimaitalia.utils.RichiediDilazioneUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Service
public class DilazioneClienteServiceImpl implements DilazioneClienteService {

    private static final Logger logger = LogManager.getLogger(AdslServiceImpl.class);

    private final ObjectMapper objectMapper;

    private final RestTemplate restTemplate;

    @Value("${restdata.urls.dilazioneCliente}")
    private String dilazioneClienteUrl;

    @Value("${restdata.urls.richiediDilazione}")
    private String richiediDilazioneUrl;

    @Value("${restdata.urls.saveRichiediDilazione}")
    private String saveRichiediDilazioneUrl;

    public DilazioneClienteServiceImpl(ObjectMapper objectMapper, RestTemplate restTemplate) {
        this.objectMapper = objectMapper;
        this.restTemplate = restTemplate;
    }

    @Override
    public DilazioneWrapper getDilazioneClienteDetail(String clientId) {
        logger.info("Obtaining list of linea details for dilazione service for client with id {}", clientId);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("cliente", clientId);

        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.dilazioneClienteUrl, HttpMethod.GET, httpEntity,
                Map.class, clientId);
        Map<String, Object> responseMap = (Map<String, Object>) exchange.getBody();

        return objectMapper.convertValue(responseMap, DilazioneWrapper.class);
    }

    @Override
    public RichiediDilazione getRichiediDilazioneDetail(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("idCliente", clientId);
        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.richiediDilazioneUrl, HttpMethod.POST, httpEntity,
                Map.class);
        Map responseMap = (Map) exchange.getBody().get("response");

        return objectMapper.convertValue(responseMap, RichiediDilazione.class);
    }

    @Override
    public LinkedHashMap saveRichiediDilazione(RichiediDilazioneRequest request, String clientId) {
        MultiValueMap headers = new HttpHeaders();
        ((HttpHeaders) headers).setContentType(MediaType.APPLICATION_JSON);
        HashMap<String, Object> body = new HashMap<>();
        body.put("idCliente", clientId);
        body.put("numRate", request.getFormModalData().getNumeroRate());
        body.put("primaRata", request.getResultFormModalData().getPrimarata());
        body.put("email", request.getFormModalData().getEmail());
        body.put("cell", request.getFormModalData().getPhoneNumber());
        body.put("flagCanoneRai", request.getFormModalData().getCanoneRai());
        body.put("casistica", RichiediDilazioneUtils.casisticaMap.get(request.getFormModalData().getCasuale()));
        body.put("cadenza", RichiediDilazioneUtils.cadenzaMap.get(request.getResultFormModalData().getCadenza()));
        body.put("importoDilazionato", Float.parseFloat(request.getFormModalData().getImportoDilazione()));
        body.put("importoRata", request.getResultFormModalData().getImportoRata());
        body.put("caseOriginCode", "200007");
        body.put("previewOnly", false);
        body.put("flagScaduto", request.getFormModalData().isFlagScaduto());
        body.put("codiceModalitaPagamento", request.getFormModalData().getModalitaPagamento());
        HttpEntity<HashMap> requestEntity = new HttpEntity<HashMap>(body, headers);

        return (LinkedHashMap) restTemplate.exchange(saveRichiediDilazioneUrl, HttpMethod.POST,
                requestEntity, Map.class).getBody();
    }
}

