package com.optimaitalia.service;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.mobile.MobilePaymentRequest;
import com.optimaitalia.model.wrappers.payment.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

public interface PaymentService {

    ModelAndView doPayment(List<Long> invoices, Long clientId) throws Exception, PaymentException;

    String rechargeSimCard(MobilePaymentRequest request) throws PaymentException, ValidateException;

    ModelAndView doPayRate(List<String> rateList, Long clientId) throws Exception, PaymentException;

    PagamentiPaymentResponse getPaymentModalitaPagamentiUrl(PagamentiPaymentRequest body);

    NexiPaymentResponse getNexiPaymentInfo(NexiPaymentRequest body);

    PagamentiPaymentResponse getAutoricaricaURL(PagamentiPaymentRequest body);

    PayPalResponse getAutoricaricaPayPalURL(ActivatePayPalAutoricaricaRequest body);

    CheckSubscriptionAutoricaricaResponse getInformationAboutAutoricarica(CheckSubscriptionAutoricaricaRequest body);

    Map deactivateAutoricarica(DeactivateAutoRicaricaRequest body);
}
