package com.optimaitalia.model.services;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;

public class AdditionalInfo {
    private String msisdn;
    private Date dateDeactivation;
    private String ICCD;
    private Date dateActivation;
    private Date dateRenewal;
    private String price;

    @JsonProperty("msisdn")
    public String getMsisdn() {
        return msisdn;
    }

    @JsonProperty("MSISDN")
    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    @JsonProperty("dateDeactivation")
    public Date getDateDeactivation() {
        return dateDeactivation;
    }

    @JsonProperty("dataDisattivazione")
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    public void setDateDeactivation(Date dateDeactivation) {
        this.dateDeactivation = dateDeactivation;
    }

    @JsonProperty("ICCD")
    public String getICCD() {
        return ICCD;
    }

    @JsonProperty("ICCD")
    public void setICCD(String ICCD) {
        this.ICCD = ICCD;
    }

    @JsonProperty("dateActivation")
    public Date getDateActivation() {
        return dateActivation;
    }

    @JsonProperty("dataAttivazione")
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    public void setDateActivation(Date dateActivation) {
        this.dateActivation = dateActivation;
    }

    @JsonProperty("price")
    public String getPrice() {
        return price;
    }

    @JsonProperty("canoneMese")
    public void setPrice(String price) {
        this.price = price;
    }

    @JsonProperty("dateRenewal")
    public Date getDateRenewal() {
        return dateRenewal;
    }

    @JsonProperty("dataRinnovoBundle")
    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setDateRenewal(Date dateRenewal) {
        this.dateRenewal = dateRenewal;
    }
}
