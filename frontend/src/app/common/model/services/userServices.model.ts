export interface UserServices {
  id: number;
  serviceName: string;
  utilities: Array<Utility>;
}

export interface Utility {
  id: number;
  utNumber: string;
  integratedProduct: boolean;
  status: string;
  startDate: Date;
  endDate: Date;
  firstActivationDate: Date;
  scrap: string;
  inizioValidita: string;
  additionalInfo: AdditionalInfo;
}

export interface AdditionalInfo {
  msisdn: string;
  dateDeactivation: Date;
  ICCD: string;
  price: string;
  dateActivation: Date;
  dateRenewal: Date;
}

export const UserNewServicesObject = (): UserServices => {
  return {
    id: null,
    serviceName: 'SERVIZI',
    utilities: []
  };
};
export const UserServicesObjects = (...serviceNames: string[]): UserServices => {
  return {
    id: null,
    serviceName: 'SERVIZI',
    utilities: serviceNames.map(name => ({
      id: null,
      utNumber: null,
      integratedProduct: false,
      status: 'Attivo',
      startDate: null,
      endDate: null,
      firstActivationDate: null,
      scrap: name,
      inizioValidita: null,
      additionalInfo: null
    }))
  };
};
