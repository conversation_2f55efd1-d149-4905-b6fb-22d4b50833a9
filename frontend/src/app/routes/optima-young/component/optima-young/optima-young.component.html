<div class="offerte-layout" *ngIf="showDefaultLayout">
  <div class="header">Offerte</div>
  <div class="client-info">Cliente: <b>{{ fullName }}</b></div>
  <div class="invite-banner-container" *ngIf="showOptimaYoungBanner && !showNoInfo">
    <img class="invite-banner" src="/assets/img/optima-young/vertical-invite-optima-young-banner.png" alt="Banner"/>
    <div class="clickable-area-optima-young" (click)="toggleLayouts('showDefaultLayout','showPortaITuoiLayout')"></div>
  </div>
  <div class="invite-banner-container" *ngIf="!showOptimaYoungBanner && !showNoInfo">
    <img class="invite-banner" src="/assets/img/optima-young/vertical-invite-mobile-banner.png" alt="Banner"/>
    <div class="clickable-area-mobile" (click)="toggleLayouts('showDefaultLayout','showPortaITuoiLayout')"></div>
  </div>
  <div class="no-info" *ngIf="showNoInfo">
    <img src="assets/img/optima-young/hourglass.png" alt="hourglass">
    <div class="title">Nessuna offerta disponibile</div>
    <div class="description">Ti invitiamo a tornare in questa sezione per essere sempre aggiornato sulle nuove offerte
    </div>
  </div>
</div>
<div class="referral-link-layout" *ngIf="showPortaITuoiLayout">
  <div class="referral-link-header">
    <img class="back-arrow" src="assets/img/optima-young/white-arrow.png" alt="Arrow"
         (click)="toggleLayouts('showPortaITuoiLayout','showDefaultLayout')"/>
    <div *ngIf="!selectedMobile.isAvailableOptimaYoung" class="title">Porta un amico in Optima</div>
    <div *ngIf="selectedMobile.isAvailableOptimaYoung" class="title">Porta i tuoi amici in Optima</div>
    <span class="agreement-link" (click)="toggleProperty('showInfoBlock')">Come funziona?</span>
    <hr/>
    <div class="select-container">
      <label for="number">Scegli il numero <img src="assets/img/optima-young/info-white.png" alt="info"
                                                (click)="toggleProperty('showTermsModal')"/></label>
      <select id="number" [(ngModel)]="selectedMobileIndex" (change)="updateSelectedMobile()">
        <option *ngFor="let mobile of generalInformation?.subscriptions; let i = index" [value]="i">
          {{ mobile.msisdnid }}
        </option>
      </select>
    </div>
  </div>

  <div *ngIf="!selectedMobile.isAvailableOptimaYoung">
    <div class="referral-link-block" *ngIf="!checkIsRewardAvailable(CouponTypes.LEVEL_1)">
      <div class="invite-header">Invita subito i tuoi amici condividendo il tuo Codice Amico</div>
      <div class="referral-link-banner">
        <div class="header-banner">1 MESE DI MOBILE GRATUITO</div>
        <div class="small">Se <b>un tuo amico</b> passa a Optima</div>
        <img class="img-gift" src="assets/img/optima-young/person-gift.png" alt="Gift"/>
        <img class="img-copy-code" src="assets/img/optima-young/button/copy-code.png"
             (click)="openModalWithCode(CouponTypes.LEVEL_1)" alt="Copy code"/>
      </div>
    </div>
    <div class="stats-block" *ngIf="checkIsRewardAvailable(CouponTypes.LEVEL_1)">
      <div class="text"><b>Un tuo amico</b> è passato a Optima</div>
      <div class="header-stats">
        <div>HAI VINTO</div>
        <div>1 MESE DI MOBILE GRATUITO</div>
      </div>
      <img src="assets/img/optima-young/gift.png" alt="gift">
      <span>Il mese gratuito parte dal giorno</span>
      <span class="font-weight-bold">{{ getRewardReceivedDate(CouponTypes.LEVEL_1) }}</span>
    </div>
  </div>

  <div *ngIf="selectedMobile.isAvailableOptimaYoung">
    <div class="invite-header">
      Invita i tuoi amici condividendo il tuo Codice per vincere i premi
    </div>
    <div class="porta-un-amico" (click)="toggleProperty('showPortaUnAmico')">
      <div>PORTA UN AMICO</div>
      <img class="img-arrow" [ngClass]="{'rotated': showPortaUnAmico}"
           src="assets/img/optima-young/white-arrow.png" alt="Arrow">
    </div>
    <div *ngIf="showPortaUnAmico && !checkIsRewardAvailable(CouponTypes.LEVEL_1)">
      <div class="header-banner special">1 MESE DI MOBILE GRATUITO</div>
      <div class="small text-center">Se <b>un tuo amico</b> passa a Optima</div>
      <div class="referral-link-banner special">
        <img class="img-gift" src="assets/img/optima-young/person-gift.png" alt="Gift"/>
        <img class="img-copy-code" src="assets/img/optima-young/button/copy-code.png"
             (click)="openModalWithCode(CouponTypes.LEVEL_1)" alt="Copy code"/>
      </div>
    </div>
    <div class="stats-block" *ngIf="showPortaUnAmico && checkIsRewardAvailable(CouponTypes.LEVEL_1)">
      <img class="star" src="assets/img/optima-young/star.png" alt="star"/>
      <div class="text"><b>Un tuo amico</b> è passato a Optima</div>
      <div class="header-stats">
        <div>HAI VINTO</div>
        <div>1 MESE DI MOBILE GRATUITO</div>
      </div>
      <img src="assets/img/optima-young/gift.png" alt="gift">
      <span>Il mese gratuito parte dal giorno</span>
      <span class="font-weight-bold">{{ getRewardReceivedDate(CouponTypes.LEVEL_1) }}</span>
    </div>

    <div *ngIf="selectedMobile?.isAvailableOptimaYoung" class="optima-young"
         (click)="toggleProperty('showOptimaYoung')">
      <div class="logo-wrapper">
        <img class="img-optima-young" src="assets/img/optima-young/optima-young-logo.png" alt="Optima Young">
        <div class="optima-young-text">OPTIMA YOUNG</div>
      </div>
      <img class="img-arrow" [ngClass]="{'rotated': showOptimaYoung}"
           src="assets/img/optima-young/white-arrow.png"
           alt="Arrow">
    </div>
    <div *ngIf="showOptimaYoung">
      <div class="image-container">
        <img [src]="getImagePath(CouponTypes.LEVEL_2)" class="img-layout" alt="layout"/>
        <div class="clickable-area" [ngClass]="{'active': isActiveButton(CouponTypes.LEVEL_2)}"
             (click)="openModalWithCode(CouponTypes.LEVEL_2)"></div>
        <div class="text-layout">{{ getRewardReceivedDate(CouponTypes.LEVEL_2) }}</div>
      </div>
      <div class="image-container">
        <img [src]="getImagePath(CouponTypes.LEVEL_3)" class="img-layout" alt="layout"/>
        <div class="clickable-area-level-3" [ngClass]="{'active': isActiveButton(CouponTypes.LEVEL_3)}"
             (click)="openModalWithCode(CouponTypes.LEVEL_3)"></div>
      </div>
      <div *ngIf="getRewardReceivedDate(CouponTypes.LEVEL_3)" class="buttons-layout">
        <!--<img class="amazon-button" src="assets/img/optima-young/button/white-download-voucher.png"
             alt="download"/>-->
        <img class="amazon-button"
             src="assets/img/optima-young/button/amazon-vouchers-won.png"
             alt="download" (click)="toggleProperty('showAmazonVoucherModal')"/>
      </div>
    </div>
    <!--    <div class="referral-mobile-banner" *ngIf="showOptimaYoung">
          <div class="header-mobile">5 MESI DI MOBILE GRATUITI</div>
          <div class="small">Se altri 3 amici passano a Optima</div>
          <div class="referral-link-banner">
            <img class="img-gift" src="assets/img/optima-young/person-gift.png" alt="Gift"/>
            <img class="img-copy-code" src="assets/img/optima-young/copy-code.png" (click)="copyToClipboard()"
                 alt="Copy code"/>
          </div>
        </div>-->
  </div>
</div>

<div class="mobile-modal-div show" *ngIf="showInfoBlock">
  <div class="mobile-inner-modal-div">
    <img class="back-button" src="assets/img/optima-young/arrow.png" alt="Back"
         (click)="toggleProperty('showInfoBlock')"/>
    <div class="info-header">Come funziona?</div>
    <div class="info-text font-weight-bold">PORTA UN AMICO IN OPTIMA MOBILE</div>
    <div class="info-text">Con “Porta un amico in Optima Mobile” puoi ottenere un mese di canone Mobile gratuito.
    </div>
    <div class="info-text">Invita i tuoi amici a passare a Optima attivando l’offerta “Super Mobile Smart” da e-commerce
      e utilizzando il tuo CODICE AMICO. Quando uno dei tuoi amici completa la procedura di portabilità del numero in
      Optima, riceverete entrambi un mese di canone gratuito.
    </div>
    <div class="info-text">Per tutti i dettagli leggi il regolamento <a
      href="https://www.optimaitalia.com/public/files/pdf/regolamenti/2024/PortaunamicoinOptimaMobile_27062024.pdf"
      target="_blank">qui</a></div>
    <hr class="custom-line"/>
    <div *ngIf="selectedMobile.isAvailableOptimaYoung">
      <div class="info-text font-weight-bold">OPTIMA YOUNG</div>
      <div class="info-text">Dopo che hai portato un amico in Optima puoi partecipare a Optima Young.</div>
      <div class="info-text">Continua ad invitare i tuoi amici di età uguale o maggiore di 30 anni e ottieni:</div>
      <div class="info-text">- 5 mesi di canone gratuiti, se 5 amici passano a Optima attivando l’offerta “Super Mobile
        Smart” da e-commerce e utilizzando il tuo codice YOUNG;
      </div>
      <div class="info-text">- un Buono Amazon da 50 €, se 10 amici passano a Optima attivando l’offerta “Super Mobile
        Smart” da e-commerce utilizzando il tuo codice YOUNG PRO, ed effettuano una ricarica.
      </div>
      <div class="info-text">Per tutti i dettagli leggi il regolamento qui</div>
    </div>
  </div>
</div>
<div class="mobile-modal-div show" *ngIf="showTermsModal">
  <div class="mobile-inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="toggleProperty('showTermsModal')"></i>
    <img class="img-info" src="assets/img/optima-young/info.png" alt="Info">
    <div class="info-text text-center">Puoi partecipare all’iniziativa a premi esclusivamente con le SIM acquistate da
      e-commerce con offerta “Super Mobile Smart”.
    </div>
  </div>
</div>
<div class="mobile-modal-div show" *ngIf="showInvalidCodeModal">
  <div class="mobile-inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="toggleProperty('showInvalidCodeModal')"></i>
    <img class="img-info" src="assets/img/optima-young/triangle-alert.png" alt="Alert">
    <div class="info-header">Attenzione il Codice non è più valido e non può essere più utilizzato.</div>
  </div>
</div>
<div class="mobile-modal-div show" *ngIf="showAmazonVoucherModal">
  <div class="mobile-inner-modal-div">
    <img class="back-button" src="assets/img/optima-young/arrow.png" alt="Back"
         (click)="toggleProperty('showAmazonVoucherModal')"/>
    <div class="info-header">Buoni Amazon vinti</div>
    <div class="tags-container">
      <div class="font-weight-bold">|TAG_Buono Amazon_1|</div>
      <div>del XX/XX/XXXX</div>
    </div>
    <img class="img-download-voucher" src="assets/img/optima-young/button/download-voucher.png" alt="Download">
  </div>
</div>
<div class="modal-div show" *ngIf="showCopyCodeModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="toggleProperty('showCopyCodeModal')"></i>
    <div class="font-weight-bold">Ecco il tuo CODICE {{ modalContent.title }}:</div>
    <div class="code-container">
      {{ modalContent.code.toLocaleUpperCase() }}
      <img src="assets/img/optima-young/copy-logo.png" alt="Copy" (click)="copyCode(modalContent.code)"/>
    </div>
    <div class="inviting-phrase">Condividilo con i tuoi amici!</div>
    <div>Il Codice può essere utilizzato fino al:</div>
    <div class="font-weight-bold">{{ modalContent.validUntil }}</div>
  </div>
</div>
<div *ngIf="copySuccess" class="modal-container">
  <div class="modal-content">
    <span class="modal-title">Il codice è stato copiato con successo!</span>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
