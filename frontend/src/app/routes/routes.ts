import {NavigationComponent} from '../layout/navigation/navigation.component';
/* guard-service */
import {AuthGuard} from '../services/auth/auth-guard.service';
import {CondominioLoadGuard} from '../services/condominioLoad.guard';

export const routes = [
  {
    path: '',
    component: NavigationComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: localStorage.getItem('access_token') ? '/home/<USER>' : '/login',
        pathMatch: 'full'
      },
      {
        path: 'home',
        loadChildren: './home/<USER>'
      },
      {
        path: 'profile',
        loadChildren: './profilePage/profilePage.module#ProfilePageModule'
      },
      {
        path: '',
        loadChildren: './additionalUserDataPage/additionalUserDataPage.module#AdditionalUserDataPageModule'
      },
      {
        path: 'invoices',
        loadChildren: './invoices/invoice.module#InvoiceModule'
      },
      {
        path: 'support',
        loadChildren: './answer-questions/answer-questions.module#AnswerQuestionsModule'
      },
      {
        path: '',
        loadChildren: './assistant/assistant.module#AssistantModule'
      },
      {
        path: '',
        loadChildren: './moduli/moduli.module#ModuliModule'
      },
      {
        path: 'faidate',
        loadChildren: './fai-da-te/fai-da-te.module#FaiDaTeModule'
      }, {
        path: 'product',
        loadChildren: './products/products.module#ProductsModule'
      },
      {
        path: 'passa-tutto-in-uno',
        loadChildren: './passa_a_tutto_in_uno/passa.module#PassaModule'
      },
      {path: 'login', loadChildren: './pages/pages.module#PagesModule'}
    ]
  },
  {
    path: 'optima-young',
    loadChildren: './optima-young/optima-young.module#OptimaYoungModule',
    canActivate: [AuthGuard],
  },
  {path: 'myoptima/:clusterType', loadChildren: './landing/landing.module#LandingModule'},
  {
    path: 'gestione-condomini',
    loadChildren: './condominio/condominio.module#CondominioModule',
    canLoad: [CondominioLoadGuard]
  },
  /*{path: 'optima-young', loadChildren: './optima-young/optima-young.module#OptimaYoungModule'},*/
  {path: '**', redirectTo: '/home/<USER>'}

];
