import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {ActivatedRoute, Router} from '@angular/router';
import {Subscription} from 'rxjs/Subscription';
import * as moment from 'moment';
import {ObservableUtils} from '../../../../../common/utils/ObservableUtils';
import {PaymentService} from '../../../../../common/services/payment/payment.service';
import SubscriptionAutoricaricaRequest, {DeactivateOrActivateAutoRicaricaRequest} from '../../../models/PaymentAutoricaricaModels';
import {UserData} from '../../../../../common/model/userData.model';
import {ActivatePayPalAutoricaricaRequest, ConfirmPaymentModelRequest} from '../../../../profilePage/model/ConfirmPaymet';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {environment} from '../../../../../../environments/environment';

declare const AxeptaSDKClient: any;

@Component({
  selector: 'app-autoricarica-information',
  templateUrl: './autoricarica-information.component.html',
  styleUrls: ['./autoricarica-information.component.scss']
})
export class AutoricaricaInformationComponent implements OnInit, OnDestroy {

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userData: UserData;
  userInfoSubscription: Subscription;
  mobileStateSubscription: Subscription;
  selectedNumber: number;
  isActive: boolean;
  status: string;
  importoAutoricarica: string;
  nextRenewalOffer: string;
  showVisaIcon: boolean;
  showMastercardIcon: boolean;
  showMaestroIcon: boolean;
  isPayPalActive: boolean;
  cardNumber: string;
  showModifyPaymentInformationWindow: boolean;
  isAutoricaricaActive: boolean;
  subscriptionId: string;
  formGroup: FormGroup;

  constructor(protected route: ActivatedRoute, private paymentService: PaymentService, protected fb: FormBuilder,
              public router: Router) {
    this.route.params.subscribe(param => {
      if (param.id) {
        this.selectedNumber = parseInt(param.id, 10);
      }
    });
    this.formGroup = this.fb.group({
      msisdnId: [this.selectedNumber, [Validators.required]],
    });
    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      this.userData = userData;
    });
    this.getAutoricaricaInformation();
  }

  ngOnInit() {
    if (environment.production === true) {
      this.paymentService.loadScript('https://pay.axepta.it/sdk/axepta-pg-redirect.js');
    } else {
      this.paymentService.loadScript('https://pay-sandbox.axepta.it/sdk/axepta-pg-redirect.js');
    }
  }

  tumblerEvent() {
    this.showModifyPaymentInformationWindow = true;
  }

  deactivateOrActivateAutoricarica() {
    if (this.isActive) {
      const request = new DeactivateOrActivateAutoRicaricaRequest(this.subscriptionId);
      this.hideModalWindow();
      this.paymentService.postDeactivateAutoricarica(request).subscribe(response => {
        if (response.success && response.success === 'true') {
          this.isActive = false;
          this.status = 'INATTIVO';
        }
      });
    } else {
      this.router.navigate([`/faidate/servizi-attivi/mobile/ricarica/${this.formGroup.controls['msisdnId'].value}/autoricarica`]);
    }
  }

  getAutoricaricaInformation() {
    const request = new SubscriptionAutoricaricaRequest();
    request.COD_CLIENTE = localStorage.getItem('clientId');
    this.mobileStateSubscription = this.contractRecords.subscribe(result => {
      for (let i = 0; i < result.length; i++) {
        if (result[i].msisdnId === parseInt(this.formGroup.controls.msisdnId.value, 10)) {
          request.SUBSCRIPTION_ID = this.subscriptionId = result[i].id.toString();
          this.importoAutoricarica = (result[i].additionalProducts[0].product.renewalPrice).toFixed(2);
          const unit = result[i].additionalProducts[0].product.renewalPeriodUnit.name.toLowerCase();
          const nextRenewalOffer = moment(result[i].additionalProducts[0].expiresOn);
          if (moment() > moment(nextRenewalOffer).subtract(2, 'days')) {
            this.nextRenewalOffer = nextRenewalOffer.add(result[i].additionalProducts[0].product.renewalPeriod, unit === 'day' ? 'd' : 'M').subtract(2, 'days').toString();
          } else {
            this.nextRenewalOffer = nextRenewalOffer.subtract(2, 'days').toString();
          }
        }
      }
    });
    this.paymentService.postPaymentAutoricaricaInformation(request).subscribe(response => {
      if (response.MaskedPan !== null) {
        this.isAutoricaricaActive = true;
        this.cardNumber = response.MaskedPan;
        if (response.RicaricaRicorrente === 1) {
          this.isActive = true;
          this.status = 'ATTIVO';
        } else {
          this.isActive = false;
          this.status = 'INATTIVO';
        }
        switch (response.Brand) {
          case 'VISA':
            this.showVisaIcon = true;
            this.showMastercardIcon = false;
            this.showMaestroIcon = false;
            this.isPayPalActive = false;
            break;
          case 'MASTERCARD':
            this.showMastercardIcon = true;
            this.showVisaIcon = false;
            this.showMaestroIcon = false;
            this.isPayPalActive = false;
            break;
          case 'MAESTRO':
            this.showMaestroIcon = true;
            this.showVisaIcon = false;
            this.showMastercardIcon = false;
            this.isPayPalActive = false;
            break;
        }
        if (response.ProviderPagamento && response.EMail) {
          this.isPayPalActive = true;
          this.cardNumber = response.EMail;
          this.isAutoricaricaActive = true;
        }
      } else {
        this.isAutoricaricaActive = false;
      }
    });
  }

  fillModifyPaymentRequest() {
    const request = new ConfirmPaymentModelRequest();
    request.CF = this.userData.fiscalCode;
    request.PIVA = this.userData.vatNumber;
    request.CodiceCliente = localStorage.getItem('clientId');
    request.AddInfo1 = localStorage.getItem('clientId');
    request.AddInfo2 = this.subscriptionId;
    request.AddInfo3 = this.selectedNumber.toString();
    return request;
  }

  fillPayPalAutoricaricaRequest() {
    const request = new ActivatePayPalAutoricaricaRequest();
    request.RagioneSociale = this.userData.nameInInvoice;
    request.CF = this.userData.fiscalCode;
    request.PIVA = this.userData.vatNumber;
    request.Data = moment().format('DD/MM/YYYY');
    request.CodiceCliente = localStorage.getItem('clientId');
    request.AddInfo1 = localStorage.getItem('clientId');
    request.AddInfo2 = this.subscriptionId;
    request.AddInfo3 = this.selectedNumber.toString();
    return request;
  }

  modifyPaymentInformation() {
    if (this.isPayPalActive) {
      this.paymentService.postPayPalAutoricarica(this.fillPayPalAutoricaricaRequest()).subscribe(response => {
        window.open(response.returnURL);
      });
    } else {
      this.paymentService.postPaymentAutoricarica(this.fillModifyPaymentRequest()).subscribe(response => {
        const axeptaClient = new AxeptaSDKClient(response.SDK, response.APILicenseKeyEasy);
        axeptaClient.proceedToPayment(response.PaymentId);
      });
    }
  }

  hideModalWindow() {
    this.showModifyPaymentInformationWindow = false;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.mobileStateSubscription, this.userInfoSubscription]);
  }
}
