<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding flex-panel" [formGroup]="formGroup">

  <div class="left-part">
    <div class="no-padding scegli-numero">
      <label for="simList">Scegli il numero</label>
      <select id="simList" class="form-control" formControlName="msisdnId"
              (change)="getInformationAboutRicarica(userData.id.toString())">
        <option value="">Ricarica altro numero</option>
        <option *ngFor="let record of contractRecordsSelect | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
    </div>
    <!--<span class="text-danger" *ngIf="(formGroup.controls['msisdnId'].hasError('required') &&
        (formGroup.controls['msisdnId'].dirty || formGroup.controls['msisdnId'].touched)) ||
        (formGroup.controls['optimaNumber'].hasError('required') && (formGroup.controls['optimaNumber'].dirty ||
        formGroup.controls['optimaNumber'].touched))">Inserisci il numero di telefono da ricaricare</span>-->
    <span class="text-info"
          *ngIf="productActivationRecord && productActivationRecord.purchasedOn"> Ultima ricarica {{productActivationRecord.purchasedOn  |date:"dd/MM/yyyy"}}
      € {{productActivationRecord.chargedAmount | abs}}
        </span>

    <div class="optima-number-block scegli-numero">
      <label for="simNumber">Altro numero</label>
      <div class="form-inline" style="display: flex; width: 102%;">
        <input id="simNumberTemplate" class="form-control form-group" type="text" value="+39" disabled>
        <input id="simNumber" class="form-control form-group" style="width: -webkit-fill-available;" type="number"
               placeholder="Numero Optima"
               formControlName="optimaNumber">
      </div>
    </div>
    <span class="text-danger" *ngIf="(formGroup.controls['optimaNumber'].hasError('required') && (formGroup.controls['optimaNumber'].dirty ||
        formGroup.controls['optimaNumber'].touched))">Inserisci il numero di telefono da ricaricare</span>
    <span class="text-danger" *ngIf="formGroup.controls['optimaNumber'].hasError('minLength') &&
       (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched)">
        Lunghezza minima 6 caratteri.
      </span>
    <span class="text-danger"
          *ngIf="formGroup.controls['optimaNumber'].hasError('numberExistence') &&
       (formGroup.controls['optimaNumber'].dirty || formGroup.controls['optimaNumber'].touched)">Il numero selezionato non è un numero appartenente alla rete Optima.</span>
    <span class="text-danger"
          *ngIf="formGroup.controls['optimaNumber'].hasError('maxLength') && (formGroup.controls['optimaNumber'].dirty ||
      formGroup.controls['optimaNumber'].touched)">Lunghezza massima 10 caratteri.</span>

    <div style="margin-top: 25px">Importo:</div>
    <img class="importo-size-img" [src]="euroImages['5']" alt="5" (click)="changeStyle('5')">
    <img class="importo-size-img" [src]="euroImages['10']" alt="10" (click)="changeStyle('10')">
    <img class="importo-size-img" [src]="euroImages['15']" alt="15" (click)="changeStyle('15')">
    <img class="importo-size-img" [src]="euroImages['20']" alt="20" (click)="changeStyle('20')">
    <img class="importo-size-img" [src]="euroImages['50']" alt="50" (click)="changeStyle('50')">
    <span class="text-danger btn-block" *ngIf="isUserChooseValue()">Scegli un importo da ricaricare</span>

    <button class="button payment-button" (click)="replenishAccount()">RICARICA</button>
  </div>

  <div class="line"></div>

  <div class="right-part">
    <div class="title">
      <img src="assets/img/payment/Autoricarica.png" alt=""/>
      <div *ngIf="!showDetailsAboutAutoricarica">Attiva l’Autoricarica</div>
      <div *ngIf="showDetailsAboutAutoricarica">Autoricarica Attivata</div>
    </div>
    <div *ngIf="!showDetailsAboutAutoricarica">
      <div class="text">Clicca sul pulsante per attivare la ricarica automatica della tua offerta per ogni mese</div>
      <button class="attiva-button" *ngIf="!((formGroup.controls['msisdnId'].hasError('required') &&
        (formGroup.controls['msisdnId'].dirty || formGroup.controls['msisdnId'].touched)) ||
        (formGroup.controls['optimaNumber'].hasError('required') && (formGroup.controls['optimaNumber'].dirty ||
        formGroup.controls['optimaNumber'].touched))) && !showDisableButton" (click)="openAutoTopUpModalWindow()">ATTIVA
        L’AUTORICARICA
      </button>
      <button class="attiva-button disable" *ngIf="(formGroup.controls['msisdnId'].hasError('required') &&
        (formGroup.controls['msisdnId'].dirty || formGroup.controls['msisdnId'].touched)) ||
        (formGroup.controls['optimaNumber'].hasError('required') && (formGroup.controls['optimaNumber'].dirty ||
        formGroup.controls['optimaNumber'].touched)) || showDisableButton" disabled>ATTIVA L’AUTORICARICA
      </button>
    </div>

    <div *ngIf="showDetailsAboutAutoricarica" class="col-lg-7 col-md-7 col-sm-7 col-x-7 details-autoricarica">
      <select id="sim" class="form-control-right-part" formControlName="msisdnId"
              (change)="getInformationAboutRicarica(userData.id.toString())">
        <option value=""></option>
        <option *ngFor="let record of contractRecordsSelect | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
      <div class="text"><b>{{importoAutoricarica}} €</b>
        il {{nextAutoRecharge | date:'dd/MM/yyyy'}}</div>
      <div class="payment-method">
        <img *ngIf="autoRicaricaInformation.Brand === 'VISA'" class="payment-icon"
             src="assets/img/payment/visa_logo.png" alt="Visa"/>
        <img *ngIf="autoRicaricaInformation.Brand === 'MASTERCARD'" class="payment-icon"
             src="assets/img/payment/mastercard_logo.png" alt="Mastercard"/>
        <img *ngIf="autoRicaricaInformation.Brand === 'MAESTRO'" class="payment-icon"
             src="assets/img/payment/maestro_logo.png" alt="Maestro"/>
        <img *ngIf="autoRicaricaInformation.ProviderPagamento === 'PAYPAL'" class="payment-icon"
             src="assets/img/payment/paypal_logo.png" alt="PayPal"/>
        <div *ngIf="autoRicaricaInformation.MaskedPan" class="text-position">
          <b>{{autoRicaricaInformation.MaskedPan}}</b></div>
        <div *ngIf="autoRicaricaInformation.ProviderPagamento === 'PAYPAL'" class="text-position black-text">
          <b>{{autoRicaricaInformation.EMail}}</b></div>
      </div>
      <button class="attiva-button"
              [routerLink]="this.router.url.replace(pathParam, formGroup.controls['msisdnId'].value) + '/autoricarica/informazioni'">
        VISUALIZZA I DETTAGLI
      </button>
    </div>

  </div>
</div>

<div class="modal-div show" *ngIf="showAutoTopUpModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindows()"></i>
    <div class="flex-modal-inside">
      <img class="auto-ricarica-img" src="assets/img/payment/Ricarica_big.png" alt=""/>
      <div class="modal-text">Con l’Autoricarica, ogni mese ricaricheremo automaticamente sul tuo numero
        <div class="modal-text"><b>{{importoAutoricarica}} €</b></div>
        prima del rinnovo dell’offerta.
      </div>
      <a class="link"
         href="https://www.optimaitalia.com/public/files/pdf/condizioni/mobile/Info%20Utili/Condizioni_Utilizzo_Ricarica_Automatica.pdf"
         target="_blank">Leggi termini e condizioni</a>
      <button class="modal-window-button"
              [routerLink]="this.router.url.replace(pathParam, formGroup.controls['msisdnId'].value) + '/autoricarica'">
        ATTIVA
      </button>
    </div>
  </div>
</div>

<div class="modal-div show"
     *ngIf="showSuccessModalWindow || showSuccessRicaricaModalWindow || showErrorModalWindow || showErrorRicaricaModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindows()"></i>
    <div class="flex-modal-inside" *ngIf="showSuccessModalWindow">
      <img class="auto-ricarica-img" src="assets/img/icons/ok.png" alt="Ok" style="width: 120px"/>
      <div class="modal-text header-modal-text">Operazione effettuata</div>
      <div class="modal-text body-modal-text">L’operazione potrebbe richiedere qualche minuto, puoi continuare la
        navigazione nella tua Area Clienti.
        L’aggiornamento della pagina “Ricarica” sarà automatico a modifica effettuata.
      </div>
      <!--<div class="modal-text margin">Torna sull’App per visualizzare i dettagli.</div>-->
      <button class="modal-window-button modal-success-window-button" (click)="openAutoRechargeEnabledModalWindow()">
        VISUALIZZA I DETTAGLI
      </button>
    </div>
    <div class="flex-modal-inside" *ngIf="showErrorModalWindow || showErrorRicaricaModalWindow">
      <img class="auto-ricarica-img" src="assets/img/icons/Alert.png" alt="Alert" style="width: 120px"/>
      <div class="modal-text header-modal-text margin-modal-text" *ngIf="showErrorModalWindow">L’attivazione di
        Autoricarica non è andata a buon fine.
      </div>
      <div class="modal-text header-modal-text margin-modal-text" *ngIf="showErrorRicaricaModalWindow">La ricarica non è
        stata effettuata si prega di riprovare
      </div>
    </div>
    <div class="flex-modal-inside" *ngIf="showSuccessRicaricaModalWindow">
      <img class="auto-ricarica-img" src="assets/img/icons/ok.png" alt="Ok" style="width: 120px"/>
      <div class="modal-text header-modal-text">Ricarica effettuata con successo</div>
    </div>
  </div>
</div>

<div class="modal-div show" *ngIf="showAutoRechargeEnabledModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindows()"></i>
    <div class="flex-modal-inside">
      <div class="modal-text header-modal-text margin-modal">Autoricarica attivata</div>
      <div class="modal-text header-modal-text"><b>{{importoAutoricarica}} €</b></div>
      <div class="modal-text">sul numero <b>{{formGroup.controls['msisdnId'].value}}</b></div>
      <div class="line-modal-window"></div>
    </div>
    <div class="modal-text position-text-modal">Prossima Autoricarica: <b
      style="float: right">{{nextAutoRecharge | date:'dd/MM/yyyy'}}</b></div>
    <div class="modal-text position-text-modal">Prossimo rinnovo dell’offerta: <b
      style="float: right">{{newNextRenewalOffer | date:'dd/MM/yyyy'}}</b></div>
  </div>
</div>
