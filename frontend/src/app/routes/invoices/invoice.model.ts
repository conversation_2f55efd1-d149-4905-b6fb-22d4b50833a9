import {ErrorStatus} from '../../common/model/ChangePersonalDataResponse';

export class Invoice {
  id: Number;
  idInvoice: String;
  idFatt: number;
  startDate: number;
  endDate: number;
  rate: Number;
  creditNote: Number;
  paymentMode: String;
  invoiceSeries: String;
  reversalInvoicing: String;
  total: Number;
  totalEvasion: Number;
  downloadUrl: String;
  oscurata: Boolean;
  validataSdi: Boolean;
  numeroFattura: String;
  spUriTrafficoVoce: string;
  opened: number;
  isSold: Boolean;
  amountTransferred: number;
}


export class Contabile {
  importoCredito: string;
  scopertoVoce: number;
  scopertoADSL: number;
  scopertoEnergia: number;
  scopertoGAS: number;
  scopertoMobile: number;
  scopertoDevice: number;
  scopertoCanoneRai: number;
  numeroCompetenzeScoperte: number;
  numeroFattureScoperte: number;
  totaleScoperto: number;
}

export class DilazioneClienteData {
  dilazione: Dilazione;
  esito: Esito;
}

export class Dilazione {
  IdCRM: string;
  codiceCliente: string;
  dataFine: string;
  dataInizio: string;
  rate: Array<Rate>;
  fatture: Array<Fatture>;
  numeroRate: number;
  totaleImporto: number;
  esito: {};
}

export class RichiediDilazione {
  fattibilita: boolean;
  maxNumRate: number;
  maxDataPrimaRata: string;
  cadenzeList: Array<Cadenze>;
  motiviList: Array<any>;
  importoDilazionabileSoloScaduto: number;
  importoDilazionabileSoloScadutoSenzaRai: number;
  importoDilazionabileTotale: number;
  importoDilazionabileTotaleSenzaRai: number;
  maxNumRateSoloScaduto: number;
  modalitaPagamentoList: Array<ModalitaPagamento>;

}

export class RichiediDilazioneDataForm {
  canoneRai: string;
  casuale: string;
  email: string;
  importoDilazione: string;
  numeroRate: string;
  phoneNumber: string;
  scadenza: number;
  scadenzaRate: string;
  scaduto: number;
  modalitaPagamento: string;
}

export class Cadenze {
  codice: string;
  descrizione: string;
}

export class ModalitaPagamento {
  codice: string;
  descrizione: string;
}

export class Rate {
  dataPagamento: string;
  dataScadenza: string;
  idRata: string;
  importoPagato: number;
  importoRata: number;
  lineId: number;
  numeroRate: number;
  statoPagamentoRata: number;
}
export class DilazioneDataForPayPal {
  idRata: string;
  Importo: number;
}
export class Fatture {
  idInvoice: string;
  numeroDocumento: string;
}

export class FattureModel {
  DataDocumento: string;
  IdInvoice: number;
  Importo: number;
  NumeroDocumento: number;
}
export class Esito {
  descrizione: string;
  esito: number;
}

export class BillingInformation {
  errorStatus: ErrorStatus;
  response: Bill[];
}

export class Bill {
  idFatt: number;
  cuu: string;
  tipo: string;
  Utenza: string;
  tipologiaUso: string;
}

export class AggregatedBillingInformation {
  idFatt: number;
  cuu: string;
  associatedBills: ShortBillInformation[] = [];
  showAssociatedBills: boolean;

  constructor(bill: Bill, symbol: string) {
    this.idFatt = bill.idFatt;
    this.cuu = bill.cuu;
    this.associatedBills.push(new ShortBillInformation(bill, symbol));
  }
}

export class ShortBillInformation {
  tipo: string;
  utenza: string;
  tipologiaUso: string;
  symbol: string;

  constructor(bill: Bill, symbol: string) {
    this.tipo = bill.tipo;
    this.utenza = bill.Utenza;
    this.tipologiaUso = bill.tipologiaUso;
    this.symbol = symbol;
  }
}

